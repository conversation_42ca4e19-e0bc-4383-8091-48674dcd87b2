# leads-intake (was Cino<PERSON> Helper)

This repo is just for documentation, planning, etc, preferably in
Markdown.

This app is separated into components within a workflow-orchestrated
microservices architecture. Each component has its own lifecycle and
its own repository.

## Naming convention

Suggestions...

### Repositories

`{domain}-{type}-{capability}`

For workers, use `{domain}-worker-{queue_suffix}`.

Domain: leads was decided in meeting 2025-09-02

E.g:

* `leads-intake-app`: GUI frontend/backend (using the leads-adapter-api to start and follow workflow)
* `leads-adapter-api`: Incoming adapter implementing a port (interface) to start and monitor workflows
* `leads-worker-plaintext`: Temporal worker with an activity for processing various formats into LLM-friendly plaintext
* `leads-worker-llm`: Temporal worker for LLM-related activities
* `leads-worker-cinode`: Temporal worker for activities and/or workflows integrating with Cinode
* `leads-worker-notifiers`: Temporal worker for activities (outbound adapters) integrating with Teams, Slack, Email, etc to send notifications or events
* `leads-worker-workflows`: Temporal worker for all end-to-end workflows

### Workflows (code-level)

`{Process}{Object}Workflow`

E.g: ProcessCallOffWorkflow, CreateAssignmentWorkflow, CreateTenderWorkflow

### Activity names (code-level)

`{Verb}{Object}`

E.g: ProcessCallOffWithLLM, TransformToPlainText, WriteToJSON

## Workflow demo MVP

Animated GIF illustrating frontend, worker, and infrastructure
components (MinIO is used to for a local S3/blob storage backend).

Code for this demo/proof-of-concept can be found at [cinode-helper-temporal-poc](https://dev.azure.com/NionIT/leads-intake/_git/cinode-helper-temporal-poc).

![Cinode Helper Temporal Workflow](cinode-helper-temporal-workflow.gif)
