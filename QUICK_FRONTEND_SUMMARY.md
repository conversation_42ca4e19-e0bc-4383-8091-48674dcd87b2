# 🚀 Quick Frontend Summary for leads-intake-app

## What is this app?

The `leads-intake-app` is a **React frontend** that allows users to upload documents (PDFs, Word docs) and track their processing through an AI-powered workflow system.

## Architecture in 30 seconds:

```
User → Frontend (leads-intake-app) → API Gateway → Temporal Workflows → AI Workers → Results
```

## Your Frontend Responsibilities:

1. **File Upload Interface** - Drag & drop, validation, progress
2. **Workflow Tracking** - Real-time status updates, progress bars
3. **Results Display** - Show processed data, AI analysis results
4. **Error Handling** - User-friendly messages, retry logic

## Key API Integration:

```javascript
// Single API endpoint to integrate with
const API_URL = 'https://leads-adapter-api.com';

// Start workflow
POST /workflows/process-lead (with file upload)

// Check status
GET /workflows/{id}/status

// Get results
GET /workflows/{id}/results

// Server-Sent Events for real-time updates (recommended)
GET /workflows/{id}/stream (SSE endpoint)
```

## Tech Stack:

- **Frontend**: React
- **API**: REST + Server-Sent Events (SSE)
- **Deployment**: Azure Container Apps
- **Monitoring**: Application Insights + Temporal Web UI

## Backend Services:

- `leads-adapter-api` - Your main API gateway
- `leads-worker-plaintext` - PDF to text conversion
- `leads-worker-llm` - AI/OpenAI processing
- `leads-worker-cinode` - CRM integration
- `leads-worker-notifiers` - Teams/Slack notifications
- Temporal - Workflow orchestration engine

## Development Flow:

1. User uploads document in your frontend
2. Frontend calls API to start workflow
3. Temporal orchestrates background workers
4. Workers process document through AI pipeline
5. Results flow back to your frontend
6. User sees processed data and notifications

## Environment URLs:

- **Dev**: `https://greensea-2681856e.swedencentral.azurecontainerapps.io`
- **Prod**: `https://orangemoss-33e6b54c.swedencentral.azurecontainerapps.io`
- **Temporal UI**: `http://localhost:8088` (for debugging workflows)

## What makes this special:

- **Microservices**: Each worker handles one specific task
- **Temporal Workflows**: Reliable, retryable, observable business processes
- **AI Integration**: OpenAI/Azure AI for document analysis
- **Real-time**: Live progress updates via Server-Sent Events
- **Cloud Native**: Fully containerized on Azure

## Your Focus Areas:

✅ **UI/UX** - Make file upload and progress tracking intuitive  
✅ **Real-time Updates** - Show workflow progress live  
✅ **Error Handling** - Graceful failures and user feedback  
✅ **Responsive Design** - Works on all devices  
✅ **Performance** - Fast uploads and smooth interactions

## You DON'T need to worry about:

❌ Microservices orchestration (Temporal handles this)  
❌ AI/LLM integration (workers handle this)  
❌ CRM integration (workers handle this)  
❌ Infrastructure scaling (Azure handles this)  
❌ Background job processing (workers handle this)

## Quick Start Checklist:

- [ ] Clone leads-intake-app repo
- [ ] Set up local dev environment
- [ ] Configure API endpoint URLs
- [ ] Build file upload component
- [ ] Implement workflow status polling
- [ ] Add results display UI
- [ ] Test with real API integration

**Bottom Line**: You build the frontend UI, the backend microservices handle all the complex document processing, AI analysis, and CRM integration automatically through Temporal workflows.
