# Azure DevOps Pipeline for Cinode Helper
# This pipeline demonstrates best practices for building and deploying container apps

trigger:
  branches:
    include:
    - main
    - develop

variables:
- group: cinode-shared
- name: imageTag
  value: '$(Build.BuildId)'
- name: imageName
  value: 'cinode-helper'

stages:
- stage: Build
  displayName: 'Build and Push Container Image'
  jobs:
  - job: BuildJob
    displayName: 'Build Container Image'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    - checkout: self
      displayName: 'Checkout Source Code'
    
    - task: AzureCLI@2
      displayName: 'Login to ACR and Build Image'
      inputs:
        azureSubscription: 'azure-build-connection'
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          echo "Logging into ACR..."
          az acr login --name $(ACR_NAME)
          
          echo "Building and pushing container image..."
          az acr build \
            --registry $(ACR_NAME) \
            --image $(imageName):$(imageTag) \
            --image $(imageName):latest \
            --file Dockerfile \
            .
          
          echo "Image built and pushed successfully:"
          echo "$(ACR_LOGIN_SERVER)/$(imageName):$(imageTag)"

- stage: DeployDev
  displayName: 'Deploy to Development'
  dependsOn: Build
  condition: succeeded()
  variables:
  - group: cinode-dev
  
  jobs:
  - deployment: DeployToACA
    displayName: 'Deploy to Azure Container Apps (Dev)'
    environment: dev
    pool:
      vmImage: 'ubuntu-latest'
    
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: none
          
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: 'azure-build-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Deploying $(imageName):$(imageTag) to $(ACA_ENVIRONMENT)"
                
                # Check if container app exists
                if az containerapp show --name $(APP_NAME) --resource-group $(RESOURCE_GROUP) >/dev/null 2>&1; then
                  echo "Container app exists, updating..."
                  az containerapp update \
                    --name $(APP_NAME) \
                    --resource-group $(RESOURCE_GROUP) \
                    --image $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag)
                else
                  echo "Container app doesn't exist, creating..."
                  az containerapp create \
                    --name $(APP_NAME) \
                    --resource-group $(RESOURCE_GROUP) \
                    --environment $(ACA_ENVIRONMENT) \
                    --image $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag) \
                    --registry-server $(ACR_LOGIN_SERVER) \
                    --registry-identity /subscriptions/$(SUBSCRIPTION_ID)/resourcegroups/$(RESOURCE_GROUP)/providers/Microsoft.ManagedIdentity/userAssignedIdentities/id-aca-dev \
                    --user-assigned /subscriptions/$(SUBSCRIPTION_ID)/resourcegroups/$(RESOURCE_GROUP)/providers/Microsoft.ManagedIdentity/userAssignedIdentities/id-aca-dev \
                    --target-port 80 \
                    --ingress external \
                    --cpu 0.25 \
                    --memory 0.5Gi \
                    --min-replicas 0 \
                    --max-replicas 3
                fi
                
                # Get the application URL
                APP_URL=$(az containerapp show --name $(APP_NAME) --resource-group $(RESOURCE_GROUP) --query "properties.configuration.ingress.fqdn" -o tsv)
                echo "Application deployed successfully!"
                echo "URL: https://$APP_URL"

- stage: DeployProd
  displayName: 'Deploy to Production'
  dependsOn: DeployDev
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  variables:
  - group: cinode-prod
  
  jobs:
  - deployment: DeployToACA
    displayName: 'Deploy to Azure Container Apps (Prod)'
    environment: prod
    pool:
      vmImage: 'ubuntu-latest'
    
    strategy:
      runOnce:
        deploy:
          steps:
          - checkout: none
          
          - task: AzureCLI@2
            displayName: 'Deploy to Azure Container Apps'
            inputs:
              azureSubscription: 'azure-build-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                echo "Deploying $(imageName):$(imageTag) to $(ACA_ENVIRONMENT)"
                
                # Check if container app exists
                if az containerapp show --name $(APP_NAME) --resource-group $(RESOURCE_GROUP) >/dev/null 2>&1; then
                  echo "Container app exists, updating..."
                  az containerapp update \
                    --name $(APP_NAME) \
                    --resource-group $(RESOURCE_GROUP) \
                    --image $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag)
                else
                  echo "Container app doesn't exist, creating..."
                  az containerapp create \
                    --name $(APP_NAME) \
                    --resource-group $(RESOURCE_GROUP) \
                    --environment $(ACA_ENVIRONMENT) \
                    --image $(ACR_LOGIN_SERVER)/$(imageName):$(imageTag) \
                    --registry-server $(ACR_LOGIN_SERVER) \
                    --registry-identity /subscriptions/$(SUBSCRIPTION_ID)/resourcegroups/$(RESOURCE_GROUP)/providers/Microsoft.ManagedIdentity/userAssignedIdentities/id-aca-prod \
                    --user-assigned /subscriptions/$(SUBSCRIPTION_ID)/resourcegroups/$(RESOURCE_GROUP)/providers/Microsoft.ManagedIdentity/userAssignedIdentities/id-aca-prod \
                    --target-port 80 \
                    --ingress external \
                    --cpu 0.5 \
                    --memory 1Gi \
                    --min-replicas 1 \
                    --max-replicas 5
                fi
                
                # Get the application URL
                APP_URL=$(az containerapp show --name $(APP_NAME) --resource-group $(RESOURCE_GROUP) --query "properties.configuration.ingress.fqdn" -o tsv)
                echo "Application deployed successfully!"
                echo "URL: https://$APP_URL"
          
          - task: AzureCLI@2
            displayName: 'Run Health Check'
            inputs:
              azureSubscription: 'azure-build-connection'
              scriptType: 'bash'
              scriptLocation: 'inlineScript'
              inlineScript: |
                APP_URL=$(az containerapp show --name $(APP_NAME) --resource-group $(RESOURCE_GROUP) --query "properties.configuration.ingress.fqdn" -o tsv)
                echo "Running health check on https://$APP_URL"
                
                # Wait for app to be ready
                sleep 30
                
                # Check if app responds
                if curl -f -s "https://$APP_URL" > /dev/null; then
                  echo "✅ Health check passed - application is responding"
                else
                  echo "❌ Health check failed - application is not responding"
                  exit 1
                fi
