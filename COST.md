# Azure Deployment Options — Updated with ACR, Logging & Functions

## Context
We need to host:
- **5 Temporal.io workers (Go)** — light, mostly idle
- **3 Node.js apps** — modest load, mostly idle  
…and an alternative **3-container** setup:
- **1 Temporal server**, **1 Go worker**, **1 Node.js (frontend/worker)**

Requirements:
- Always-on containers (no scale-to-zero), HTTPS for web/API
- Prefer managed option (**ACA Consumption**) but compare **ACI**, **AKS**, **VM**, **App Service**
- Include **Azure Container Registry (ACR Basic, 10 GB storage)** and **logging with Application Insights (Basic via Log Analytics)**
- Also consider **Durable Functions** alternative (replacing Temporal)

## TL;DR — Recommended
**Azure Container Apps (ACA — Consumption)** for both 3-container and 8-container scenarios (mostly idle):
- Built-in HTTPS (free managed certs), simple public ingress for web/API, internal workers.
- **Compute cost (mostly idle):**
  - **3 containers:** ~**$28/mo**
  - **8 containers:** ~**$63/mo**
- Add-ons:
  - **ACR Basic (10 GB):** ~**$5/mo**
  - **Logging (App Insights via Log Analytics):** light logs may fit in free 5 GB/mo; otherwise **~$2.30–$2.76 per GB**.
- Typical all-in (light logs): **3 containers ≈ $33/mo**, **8 containers ≈ $68/mo**.

## Cost Details (Monthly, ~730 h)

### A. Azure Container Apps (ACA — Consumption)
**Idle pricing assumptions** (replicas mostly below active CPU threshold):
- **3 containers (0.5/1 + 0.25/0.5 + 0.5/1 vCPU/GiB):**
  - Billable vCPU-s after free grant ≈ 3.105M → **$9.32**
  - Billable GiB-s after free grant ≈ 6.210M → **$18.63**
  - **Compute subtotal:** **~$27.95**
- **8 containers (5×0.25/0.5 + 3×0.5/1):**
  - Billable vCPU-s after free grant ≈ 7.047M → **$21.14**
  - Billable GiB-s after free grant ≈ 14.094M → **$42.28**
  - **Compute subtotal:** **~$63.42**

**Add-ons (same for both ACA scenarios):**
- **ACR Basic (10 GB storage):** **~$5/mo**  
- **Logging (App Insights via Log Analytics):**
  - **Free allowance:** **5 GB/month**  
  - **Beyond free:** **~$2.30–$2.76 per GB** ingested

**Totals (ACA):**
- **3 containers (light logs ≤5 GB/mo):** ≈ **$33/mo**
- **3 containers (30 GB logs/mo):** ≈ **$102–116/mo**
- **8 containers (light logs ≤5 GB/mo):** ≈ **$68/mo**
- **8 containers (30 GB logs/mo):** ≈ **$137–151/mo**

#### Scenario: 8 ACA containers with mixed minReplicas

Assumption: total 8 containers where 2 largest services keep minReplicas=0 (allow scale-to-zero), remaining 6 services keep minReplicas=1 (always-on). Sizes follow the earlier 8-container mix (5×0.25 vCPU/0.5 GiB and 3×0.5 vCPU/1 GiB), with the two largest being from the 0.5/1 group.

- Always-on portion (6 replicas):
  - 5× 0.25 vCPU / 0.5 GiB
  - 1× 0.5 vCPU / 1 GiB
  - Estimated monthly compute (after free grant, mostly idle): ~the 8-container baseline minus ~2/3 of a 0.5/1 replica equivalent in free grant overlap. For practical budgeting, a good estimate is roughly 75–80% of the 8-container baseline.
  - Approximate compute subtotal: 0.78 × $63.42 ≈ **$49–52/mo**

- Scale-to-zero portion (2 replicas at 0.5/1):
  - When idle (0 replicas): ~$0 compute cost
  - Cold starts on demand; brief runtime adds small incremental cost depending on traffic

- Resulting ACA compute (mixed policy): ≈ **$49–52/mo** (plus any burst usage for the two scale-to-zero services)

Add-ons remain the same (ACR ≈ $5/mo, Logs per usage). Overall monthly with light logs typically ≈ **$54–57/mo**.

### B. Azure Container Instances (ACI)
**Always-on pricing (single rate):**
- **3 containers:** ≈ **$65.7/mo**
- **8 containers:** ≈ **$144.5/mo**

**Add-ons:**
- **ACR Basic:** ~**$5/mo**
- **Logging:** same as ACA
- **Public IP:** a few $ if assigned

**Totals (ACI):**
- **3 containers (light logs):** ≈ **$71/mo**  
- **8 containers (light logs):** ≈ **$150/mo**

### C. Other Options (unchanged)
- **AKS (min system pool 2× 4 vCPU/8 GB):** ≈ **$270–330/mo** (+ LB/IP)  
- **Single VM (B1ms + 128 GB SSD, self-managed HTTPS):** ≈ **$18–20/mo**  
- **App Service (B1/S1):** ≈ **$55–69/mo**

## D. Alternative Architecture — Azure Functions + Durable Functions + ACA Frontend

Replace Temporal with **Durable Functions**.  
Keep **1 ACA (Node.js)** frontend for API/web.  
Assume 8 functions (20 runs/day each, 30 days): 1 heavy “LLM” @10s, 7 lighter @~4s.  

### Execution
- **Executions:** 4,800/mo < 1M free → **$0**  
- **Compute:** ~11,400 GB-s < 400k free → **$0**  
- **Durable overhead:** ~**$1–2/mo**  

### Data ingest (first 2 functions)
- 100 MB/run × 1,200 runs/mo = ~120 GB/mo into Blob Hot  
- **Storage:** ≈ **$2.16/mo**  
- **Write ops:** < $0.10/mo  

### ACA Frontend
- Node.js 0.5 vCPU / 1 GiB, idle → **~$11/mo**  

### ACR + Logs
- **ACR Basic:** ~**$5/mo**  
- **Logs:** free ≤5 GB; beyond, ~$2.30–2.76/GB  

### Total (Functions architecture)
- Functions exec: $0  
- Durable: $1–2  
- Blob: $2.16  
- ACA frontend: $11  
- ACR: $5  
- Logs: $0 (light)  
- **Grand total:** **≈ $19–21/mo** (light logs)  
- With 30 GB logs: +$69–83 → **≈ $90–105/mo**

### Tradeoffs
- **Pros:** Extremely cheap if workloads light.  
- **Cons:** Higher dev complexity (Durable vs Temporal), slower iteration, harder local dev/test, cold starts possible.

## Feature/Cost Summary

| Option | Compute (mostly idle) | ACR Basic | Logging (≤5 GB) | Extras | Total (light logs) |
|---|---:|---:|---:|---|---:|
| **ACA (3 containers)** | $28 | $5 | $0 | – | **$33** |
| **ACA (8 containers)** | $63 | $5 | $0 | – | **$68** |
| **ACA (8 containers, mixed minReplicas)** | $49–52 | $5 | $0 | – | **$54–57** |
| **ACI (3 containers)** | $66 | $5 | $0 | – | **$71** |
| **ACI (8 containers)** | $145 | $5 | $0 | – | **$150** |
| **AKS (floor)** | $270–330 | n/a | $0 | Needs LB/IP | $270+ |
| **VM (B1ms)** | $18–20 | n/a | $0 | DIY TLS | $18–20 |
| **App Service (S1)** | $69 | n/a | $0 | TLS incl. | $69 |
| **Functions + Durable + ACA** | $0 | $5 | $0 | Durable $1–2 + Blob $2 + ACA $11 | **$19–21** |

## References
- **ACA pricing & billing**: https://learn.microsoft.com/en-us/azure/container-apps/billing  
- **ACI pricing**: https://azure.microsoft.com/en-us/pricing/details/container-instances/  
- **ACR pricing**: https://azure.microsoft.com/en-us/pricing/details/container-registry/  
- **Functions pricing**: https://azure.microsoft.com/en-us/pricing/details/functions/  
- **Durable billing**: https://learn.microsoft.com/en-us/azure/azure-functions/durable/durable-functions-billing  
- **Blob pricing**: https://azure.microsoft.com/en-us/pricing/details/storage/blobs/  
- **Log Analytics/App Insights**: https://azure.microsoft.com/en-us/pricing/details/monitor/  
