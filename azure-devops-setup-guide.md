# Azure DevOps Setup Guide for Cinode Helper

## Overview
This guide covers the complete setup of Azure DevOps for the Cinode Helper project with proper security practices using managed identities and environment protection.

## Step 1: Azure Infrastructure Setup

### 1.1 Prerequisites
- Azure CLI installed and configured
- Logged into Azure with appropriate subscription permissions
- Location: Sweden Central

### 1.2 Resource Provider Registration
```bash
# Register required Azure resource providers
az provider register --namespace Microsoft.ContainerRegistry
az provider register --namespace Microsoft.App
az provider register --namespace Microsoft.OperationalInsights
az provider register --namespace Microsoft.Storage
```

### 1.3 Create Resource Groups
```bash
# Create the three resource groups
az group create --name rg-dev --location "Sweden Central"
az group create --name rg-prod --location "Sweden Central"
az group create --name rg-shared --location "Sweden Central"
```

### 1.4 Create Azure Container Registry
```bash
# Create ACR in shared resource group
az acr create --resource-group rg-shared --name acrcinodehelper --sku Basic --location "Sweden Central"
```

### 1.5 Create Storage Account for Logs
```bash
# Create storage account for container console logs
az storage account create --name stcinodehelperlog --resource-group rg-shared --location "Sweden Central" --sku Standard_LRS --kind StorageV2
```

### 1.6 Create Managed Identities
```bash
# Create managed identities for each environment and build process
az identity create --name id-aca-dev --resource-group rg-dev --location "Sweden Central"
az identity create --name id-aca-prod --resource-group rg-prod --location "Sweden Central"
az identity create --name id-build-cinode --resource-group rg-shared --location "Sweden Central"
```

### 1.7 Create Azure Container Apps Environments
```bash
# Create ACA environments with blob storage logging
az containerapp env create --name aca-env-dev --resource-group rg-dev --location "Sweden Central" --logs-destination azure-monitor --storage-account /subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-shared/providers/Microsoft.Storage/storageAccounts/stcinodehelperlog

az containerapp env create --name aca-env-prod --resource-group rg-prod --location "Sweden Central" --logs-destination azure-monitor --storage-account /subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-shared/providers/Microsoft.Storage/storageAccounts/stcinodehelperlog
```

### 1.8 Configure Diagnostic Settings (Console Logs Only)
```bash
# Update diagnostic settings to only capture console logs, not system logs
az monitor diagnostic-settings update --name diagnosticsettings --resource /subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-dev/providers/Microsoft.App/managedEnvironments/aca-env-dev --logs '[{"category":"ContainerAppConsoleLogs","enabled":true},{"category":"ContainerAppSystemLogs","enabled":false}]'

az monitor diagnostic-settings update --name diagnosticsettings --resource /subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-prod/providers/Microsoft.App/managedEnvironments/aca-env-prod --logs '[{"category":"ContainerAppConsoleLogs","enabled":true},{"category":"ContainerAppSystemLogs","enabled":false}]'
```

### 1.9 RBAC Setup (Requires Admin Privileges)
```bash
# Grant AcrPush access to build identity (includes AcrPull)
az role assignment create --assignee "a78489e2-5722-4257-9c8c-952b984a397c" --role "AcrPush" --scope "/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-shared/providers/Microsoft.ContainerRegistry/registries/acrcinodehelper"

# Grant AcrPull access to dev identity
az role assignment create --assignee "b4fbcd25-ecdd-4b1c-a2fc-6a56f94290a0" --role "AcrPull" --scope "/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-shared/providers/Microsoft.ContainerRegistry/registries/acrcinodehelper"

# Grant AcrPull access to prod identity
az role assignment create --assignee "dad9d787-3e85-4dbd-92f6-600cf84067f6" --role "AcrPull" --scope "/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-shared/providers/Microsoft.ContainerRegistry/registries/acrcinodehelper"

# Grant build identity Contributor access to dev resource group (for deployments)
az role assignment create --assignee "a78489e2-5722-4257-9c8c-952b984a397c" --role "Contributor" --scope "/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-dev"

# Grant build identity Contributor access to prod resource group (for deployments)
az role assignment create --assignee "a78489e2-5722-4257-9c8c-952b984a397c" --role "Contributor" --scope "/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-prod"
```

## Step 2: Service Connections Setup

### 2.1 Build Service Connection
Create a service connection for CI/CD build processes:

1. Go to **Project Settings** → **Service connections**
2. Click **New service connection** → **Azure Resource Manager**
3. **Choose Identity Type**: Select **"Managed identity"**
4. **Step 1 - Identity Settings:**
   - **Identity type**: Managed identity
   - **Subscription for managed identity**: CinodeAssignment
   - **Resource group for managed identity**: `rg-shared`
   - **Managed identity**: `id-build-cinode`
5. **Step 2 - Azure Scope:**
   - **Scope level for service connection**: Subscription
   - **Subscription for service connection**: CinodeAssignment
   - **Resource group for Service connection**: **Leave EMPTY** (important for multi-RG access)
6. **Step 3 - Service Connection Details:**
   - **Service Connection Name**: `azure-build-connection`
   - **Description**: `Build and deployment connection using managed identity`
   - **Security**: ✅ **Check** "Grant access permission to all pipelines"

### 2.2 Environment-Specific Service Connections (Optional)
For even better security isolation, you can create separate connections for deployments:

**Dev Connection:**
- **Service Principal ID**: `401e6a10-cff1-440c-bc65-c5226ed5bd3d` (Dev identity client ID)
- **Service connection name**: `azure-dev-connection`

**Prod Connection:**
- **Service Principal ID**: `b8e6315a-9f2e-4038-bf08-7b61f3540571` (Prod identity client ID)  
- **Service connection name**: `azure-prod-connection`

## Step 3: Environment Setup

### 3.1 Create Dev Environment
1. Go to **Pipelines** → **Environments**
2. Click **New environment**
3. Configure:
   - **Name**: `dev`
   - **Description**: `Development environment for Cinode Helper`
   - **Resource**: None (we'll use it for approval gates only)

**Protection Rules for Dev:**
- No approvals required (automatic deployment)
- Optional: Add **Branch control** to restrict to `main` branch only

### 3.2 Create Prod Environment
1. Create new environment:
   - **Name**: `prod` 
   - **Description**: `Production environment for Cinode Helper`

**Protection Rules for Prod:**
1. **Required reviewers**: Add yourself and any other stakeholders
2. **Wait for approval**: 4320 minutes (3 days max wait)
3. **Branch control**: Restrict to `main` branch only
4. **Business hours**: Optional - restrict deployments to business hours only

## Step 4: Variable Groups (Optional but Recommended)

### 4.1 Shared Variables
Create a variable group: **cinode-shared**
```
ACR_NAME: acrcinodehelper
ACR_LOGIN_SERVER: acrcinodehelper.azurecr.io
SUBSCRIPTION_ID: 38336def-48f4-4edb-9174-5ab317ee4e1c
LOCATION: swedencentral
```

### 4.2 Environment-Specific Variables
**cinode-dev:**
```
RESOURCE_GROUP: rg-dev
ACA_ENVIRONMENT: aca-env-dev
APP_NAME: cinode-helper-dev
```

**cinode-prod:**
```
RESOURCE_GROUP: rg-prod
ACA_ENVIRONMENT: aca-env-prod
APP_NAME: cinode-helper-prod
```

## Step 5: Pipeline Security Best Practices

### 5.1 Service Connection Security
- Grant **minimum required permissions** to each service connection
- Use **managed identities** instead of service principal secrets
- Regularly **audit and rotate** any service principal credentials
- **Restrict pipeline permissions** on service connections

### 5.2 Environment Protection
- **Always require manual approval** for production deployments
- Use **branch policies** to ensure only tested code reaches production
- Consider **deployment slots** or **blue-green deployments** for zero-downtime updates
- **Monitor deployment logs** and set up alerts for failures

### 5.3 Secret Management
- Store **sensitive values in Azure Key Vault**
- Use **variable groups linked to Key Vault** for secret management
- **Never commit secrets** to source code
- Use **pipeline variables with encryption** for non-Key Vault secrets

## Resource Summary

### Managed Identities Created:
- **Build Identity**: `id-build-cinode` (Principal ID: `a78489e2-5722-4257-9c8c-952b984a397c`)
  - Role: `AcrPush` on ACR, `Contributor` on rg-dev and rg-prod
- **Dev Identity**: `id-aca-dev` (Principal ID: `b4fbcd25-ecdd-4b1c-a2fc-6a56f94290a0`)
  - Role: `AcrPull` on ACR
- **Prod Identity**: `id-aca-prod` (Principal ID: `dad9d787-3e85-4dbd-92f6-600cf84067f6`)
  - Role: `AcrPull` on ACR

### Infrastructure:
- **ACR**: `acrcinodehelper.azurecr.io`
- **Storage**: `stcinodehelperlog.blob.core.windows.net` (for container logs)
- **ACA Dev**: `aca-env-dev` in `rg-dev`
- **ACA Prod**: `aca-env-prod` in `rg-prod`

### Container App Endpoints:
- **Dev**: `https://greensea-2681856e.swedencentral.azurecontainerapps.io`
- **Prod**: `https://orangemoss-33e6b54c.swedencentral.azurecontainerapps.io`

## Next Steps
1. ✅ Verify all Azure infrastructure is deployed correctly
2. ✅ Run the RBAC setup commands with admin privileges
3. ✅ Create the service connections in Azure DevOps
4. ✅ Set up the environments with proper protection rules
5. Create and test the CI/CD pipeline
6. Deploy your first container application!

---

## Changelog

### 2025-09-02 - RBAC Setup Completed ✅

**Infrastructure Status: COMPLETE**

All RBAC role assignments have been successfully configured using Azure PIM (Privileged Identity Management):

#### ✅ ACR Permissions Configured:
- **Build Identity** (`b5d20b09-2a1f-46eb-a07a-27096bf6d487`): **AcrPush** role
- **Dev Identity** (`401e6a10-cff1-440c-bc65-c5226ed5bd3d`): **AcrPull** role  
- **Prod Identity** (`b8e6315a-9f2e-4038-bf08-7b61f3540571`): **AcrPull** role

#### ✅ Resource Group Permissions Configured:
- **Build Identity** (`b5d20b09-2a1f-46eb-a07a-27096bf6d487`): **Contributor** role on both `rg-dev` and `rg-prod`

#### 🔐 Security Model Achieved:
- ✅ **Least Privilege**: Each identity has minimal required permissions
- ✅ **Separation of Concerns**: Build vs Runtime identities have different roles
- ✅ **Environment Isolation**: Dev/Prod identities cannot cross-access resources
- ✅ **No Secrets**: All authentication uses managed identities

#### 🚀 Ready for Deployment:
The infrastructure is now **100% ready** for Azure DevOps CI/CD pipeline implementation. All service connection details and managed identity Client IDs are available in the Resource Summary section above.

**Note**: RBAC setup required Owner role activation through Azure PIM due to time-bound elevated privileges policy.

### 2025-09-02 - Service Connection Setup Completed ✅

**Azure DevOps Integration: READY**

Managed identity service connection has been successfully configured:

#### ✅ Service Connection Created:
- **Connection Name**: `azure-build-connection` (ID: `3f2c1791-346a-4012-9760-ab0ec9f41347`)
- **Type**: Azure Resource Manager with Managed Identity
- **Identity**: `id-build-cinode` from `rg-shared`
- **Scope**: Subscription-level (multi-resource group access)
- **Status**: Ready and verified

#### 🔄 Modern Azure DevOps Interface:
Documentation updated to reflect the current Azure DevOps UI:
- ✅ **Identity Type**: "Managed identity" (not legacy Service Principal)
- ✅ **Step-by-step wizard**: 3-step process for managed identity setup
- ✅ **Multi-RG Access**: Resource group scope left empty for cross-RG deployments
- ✅ **Pipeline Permissions**: Granted access to all pipelines for simplified usage

#### 🎯 Ready for Pipeline Deployment:
The service connection is now ready to be used by Azure DevOps pipelines for:
- Building and pushing container images to ACR
- Deploying to dev environment (automatic)
- Deploying to prod environment (with approval gates)

### 2025-09-02 - Azure DevOps Environments Created ✅

Two environments were created in Azure DevOps for release governance and approvals.

#### ✅ Environments:
- `dev` — Development environment. No approvals required (auto-deploy).
- `prod` — Production environment. Manual approval required before deployment.

#### 🧭 How they were created (UI steps):
1. Navigate to: Pipelines → Environments → New environment
2. Create `dev`:
   - Name: `dev`
   - Description: `Development environment for Cinode Helper`
   - Leave protection rules empty (no approvals)
3. Create `prod`:
   - Name: `prod`
   - Description: `Production environment for Cinode Helper`
   - Add protection rules:
     - Required reviewers: add production approvers
     - Optional: Branch control → only `main`
     - Optional: Business hours window

#### 🔗 Verification:
- Confirmed via CLI that both environments exist: `dev`, `prod`.

These environments are referenced by the pipeline stages DeployDev and DeployProd and enable approval gates and history tracking directly in Azure DevOps.
