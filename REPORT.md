# Azure DevOps Read-Only Report — Cinode Helper

Generated: 2025-09-02

This report captures a read-only inventory of the Azure DevOps environment referenced by this repository’s README, gathered via the Azure CLI (`az`) with the `azure-devops` extension. No write operations were performed.

## Summary

- Organization: https://dev.azure.com/NionIT
- Project: Cinode Helper (private, Agile process)
- Repositories: 3 (Cinode Helper, cinode-helper-temporal-poc, ops-devops-smoketest)
- Pipelines: 1 YAML pipeline (Cinode Helper Smoke Deploy) on `ops-devops-smoketest`
- Latest Run: Succeeded (manual), build number 20250902.1
- Agent Pools/Queues: Standard hosted pools/queues available; no self-hosted agents in Default
- Service Connections: 1 (azure-build-connection, azurerm, Workload Identity Federation)
- Environments: dev, prod
- Library Variable Groups: none
- Project Security Groups: default sets present
- Repo Policies: none at project scope

## Context From Repository

- README references the demo/PoC repo: `cinode-helper-temporal-poc`
- A sample pipeline YAML is present in this repo: `sample-azure-pipelines.yaml`

## Tooling

- Azure CLI: 2.77.0
- Extensions: `azure-devops 1.0.2`, `account 0.2.5`, `application-insights 1.2.3`
- Note: CLI required writing its own cache/logs under `~/.azure`; elevated permission was granted for those internal writes only. All Azure DevOps interactions were read-only list/show operations.

## Organization

- URL: https://dev.azure.com/NionIT
- Projects (read):
  - ID: `cd076ee2-b6d2-4e00-a8a5-5c722ad7a118`, Name: `Cinode Helper`, Visibility: `Private`

## Project: Cinode Helper

- ID: `cd076ee2-b6d2-4e00-a8a5-5c722ad7a118`
- Process: Agile
- Version Control: Git enabled, TFVC disabled
- Default Team: `Cinode Helper Team`

### Teams

- `Cinode Helper Team` (default project team)

### Repositories

- `Cinode Helper` (default branch `main`)
- `cinode-helper-temporal-poc` (default branch `main`)
- `ops-devops-smoketest` (default branch `main`)

Branches in `Cinode Helper`:
- `main`

### Pipelines

- Definition: `Cinode Helper Smoke Deploy` (ID 16)
  - Description: Smoke test deployment pipeline for end-to-end CI/CD validation
  - Type: YAML (`azure-pipelines-smoke.yaml`)
  - Repository: `ops-devops-smoketest`
  - Queue: `Hosted Ubuntu 1604`
  - Status: enabled
  - Triggers: continuous integration

Recent run(s):
- Run ID 327 — Number `20250902.1` — Status `completed` — Result `succeeded` — Reason `manual` — Branch `main`

### Agent Queues / Pools

Queues (project view):
- Default (Pool Type: automation, Hosted: False)
- Hosted (Hosted: True)
- Hosted VS2017 (Hosted: True)
- Hosted Windows 2019 with VS2019 (Hosted: True)
- Hosted Windows Container (Hosted: True)
- Hosted macOS (Hosted: True)
- Hosted macOS High Sierra (Hosted: True)
- Hosted Ubuntu 1604 (Hosted: True)
- Azure Pipelines (Hosted: True)

Pools (org view):
- Default (Hosted: False)
- Hosted (Hosted: True)
- Hosted VS2017 (Hosted: True)
- Hosted Windows 2019 with VS2019 (Hosted: True)
- Hosted Windows Container (Hosted: True)
- Hosted macOS (Hosted: True)
- Hosted macOS High Sierra (Hosted: True)
- Hosted Ubuntu 1604 (Hosted: True)
- Azure Pipelines (Hosted: True)

Agents in `Default` pool: none listed

### Service Connections

- Name: `azure-build-connection`
  - Type: `azurerm`
  - Auth Scheme: Workload Identity Federation (Entra ID)
  - Ready: True
  - Subscription: `CinodeAssignment` (`38336def-48f4-4edb-9174-5ab317ee4e1c`)
  - Tenant: `5c9b264f-33ad-4093-bb65-8d14aaec9f63`
  - SPN (client) ID: `b5d20b09-2a1f-46eb-a07a-27096bf6d487`
  - SPN Object ID: `a78489e2-5722-4257-9c8c-952b984a397c`

### Environments

- `dev` — “Development environment.”
- `prod` — “Production environment.”

### Library

- Variable Groups: none

### Security Groups (project)

Representative groups present:
- Project Administrators
- Project Valid Users
- Contributors
- Readers
- Build Administrators
- Release Administrators
- Endpoint Administrators
- Endpoint Creators
- Cinode Helper Team

### Repository Policies (project scope)

- None configured

## Limitations and Notes

- Only read/list/show commands were executed. No changes were made.
- Some areas (e.g., classic Releases, variable groups) returned empty results, which could mean none exist or access is restricted.
- Environments were listed via REST (`az devops invoke`) since the CLI subgroup wasn’t available.

## Appendix: Commands Executed

```
az --version
az devops project list --org https://dev.azure.com/NionIT -o table
az devops project show --org https://dev.azure.com/NionIT --project "Cinode Helper" -o json
az repos list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az repos ref list --org https://dev.azure.com/NionIT --project "Cinode Helper" --repository "Cinode Helper" -o table
az pipelines list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az pipelines show --id 16 --org https://dev.azure.com/NionIT --project "Cinode Helper" -o json
az pipelines runs list --org https://dev.azure.com/NionIT --project "Cinode Helper" --pipeline-ids 16 --top 5 -o table
az pipelines queue list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az pipelines pool list --org https://dev.azure.com/NionIT -o table
az pipelines agent list --pool-id 1 --org https://dev.azure.com/NionIT -o table
az devops team list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az devops security group list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az devops service-endpoint list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az devops service-endpoint show --id 3f2c1791-346a-4012-9760-ab0ec9f41347 --org https://dev.azure.com/NionIT --project "Cinode Helper" -o json
az pipelines variable-group list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o json
az pipelines release list --org https://dev.azure.com/NionIT --project "Cinode Helper" -o table
az devops invoke --area distributedtask --resource environments --route-parameters project="Cinode Helper" --org https://dev.azure.com/NionIT -o json
```

## Appendix: Selected Raw Outputs

Project details (JSON):

```json
{
  "abbreviation": null,
  "capabilities": {
    "processTemplate": {
      "templateName": "Agile",
      "templateTypeId": "adcc42ab-9882-485e-a3ed-7678f01f66bc"
    },
    "versioncontrol": {
      "gitEnabled": "True",
      "sourceControlType": "Git",
      "tfvcEnabled": "False"
    }
  },
  "defaultTeam": {
    "id": "56d8f6b6-8e26-48d5-82c0-b4fe15b1b811",
    "name": "Cinode Helper Team",
    "url": "https://dev.azure.com/NionIT/_apis/projects/cd076ee2-b6d2-4e00-a8a5-5c722ad7a118/teams/56d8f6b6-8e26-48d5-82c0-b4fe15b1b811"
  },
  "defaultTeamImageUrl": null,
  "description": null,
  "id": "cd076ee2-b6d2-4e00-a8a5-5c722ad7a118",
  "lastUpdateTime": "2025-08-29T11:25:56.677Z",
  "name": "Cinode Helper",
  "revision": 177,
  "state": "wellFormed",
  "url": "https://dev.azure.com/NionIT/_apis/projects/cd076ee2-b6d2-4e00-a8a5-5c722ad7a118",
  "visibility": "private"
}
```

Pipeline definition (JSON excerpt):

```json
{
  "id": 16,
  "name": "Cinode Helper Smoke Deploy",
  "description": "Smoke test deployment pipeline for end-to-end CI/CD validation",
  "process": { "type": 2, "yamlFilename": "azure-pipelines-smoke.yaml" },
  "queue": { "id": 162, "name": "Hosted Ubuntu 1604" },
  "repository": {
    "id": "034e127b-971f-4767-9a0d-2b9245dbe50e",
    "name": "ops-devops-smoketest",
    "type": "TfsGit",
    "url": "https://dev.azure.com/NionIT/Cinode%20Helper/_git/ops-devops-smoketest"
  },
  "triggers": [ { "triggerType": "continuousIntegration" } ]
}
```

Recent run (table):

```
Run ID    Number      Status     Result     Pipeline ID    Pipeline Name               Source Branch    Queued Time                 Reason
--------  ----------  ---------  ---------  -------------  --------------------------  ---------------  --------------------------  --------
327       20250902.1  completed  succeeded  16             Cinode Helper Smoke Deploy  main             2025-09-02 22:47:15.299620  manual
```

Service connection (JSON excerpt):

```json
{
  "name": "azure-build-connection",
  "type": "azurerm",
  "isReady": true,
  "authorization": {
    "scheme": "WorkloadIdentityFederation",
    "parameters": {
      "tenantid": "5c9b264f-33ad-4093-bb65-8d14aaec9f63",
      "serviceprincipalid": "b5d20b09-2a1f-46eb-a07a-27096bf6d487",
      "scope": "/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c"
    }
  },
  "data": {
    "subscriptionId": "38336def-48f4-4edb-9174-5ab317ee4e1c",
    "subscriptionName": "CinodeAssignment",
    "identityType": "ManagedIdentity"
  }
}
```

Environments (JSON excerpt):

```json
{
  "count": 2,
  "value": [
    { "id": 3, "name": "dev",  "description": "Development environment." },
    { "id": 4, "name": "prod", "description": "Production environment." }
  ]
}
```

## Deployed Apps — Dev

Type: Azure Container App

- Name: `cinode-helper-dev`
- Resource Group: `rg-dev`
- Location: Sweden Central
- Status: Running (active revision `cinode-helper-dev--9tjurzc`, traffic 100%)
- Ingress: External, FQDN `cinode-helper-dev.greensea-2681856e.swedencentral.azurecontainerapps.io` (targetPort 80)
- Image: `mcr.microsoft.com/azuredocs/containerapps-helloworld:latest`
- Scale: `minReplicas: 0`, `maxReplicas: 1` (may scale to zero when idle)
- Workload Profile: `Consumption`
- Managed Environment: `/subscriptions/38336def-48f4-4edb-9174-5ab317ee4e1c/resourceGroups/rg-dev/providers/Microsoft.App/managedEnvironments/aca-env-dev`
- Identity: User-assigned (`id-aca-dev`), clientId `401e6a10-cff1-440c-bc65-c5226ed5bd3d`

Container App (JSON excerpt):

```json
{
  "name": "cinode-helper-dev",
  "location": "Sweden Central",
  "resourceGroup": "rg-dev",
  "properties": {
    "runningStatus": "Running",
    "latestReadyRevisionName": "cinode-helper-dev--9tjurzc",
    "configuration": {
      "ingress": {
        "external": true,
        "fqdn": "cinode-helper-dev.greensea-2681856e.swedencentral.azurecontainerapps.io",
        "targetPort": 80
      }
    },
    "template": {
      "containers": [
        { "name": "cinode-helper-dev", "image": "mcr.microsoft.com/azuredocs/containerapps-helloworld:latest" }
      ],
      "scale": { "minReplicas": 0, "maxReplicas": 1 }
    }
  }
}
```
