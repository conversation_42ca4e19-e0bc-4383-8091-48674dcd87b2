# 🏗️ Leads Intake Frontend Architecture Guide

## Overview
This document explains the microservices architecture for the **leads-intake-app** frontend and how it integrates with the backend services.

## 🎯 What is leads-intake-app?
The `leads-intake-app` is the **frontend application** (React/Vue/Angular) that provides the user interface for the leads intake system. It's your main responsibility as a frontend developer.

## 🔄 Architecture Flow
```
User → leads-intake-app → leads-adapter-api → Temporal → Worker Services → External APIs
```

## 🏛️ System Components

### **Frontend Layer (Your Domain)**
- **leads-intake-app**: React/Vue frontend application (Port: 3000)
  - User interface for uploading documents
  - Workflow status tracking
  - Results display
  - File management

### **API Gateway**
- **leads-adapter-api**: REST API Gateway (Port: 8080)
  - Single entry point for your frontend
  - Handles authentication & validation
  - Starts and monitors workflows
  - Returns results to frontend

### **Workflow Engine**
- **Temporal Server**: Workflow orchestrator (Port: 7233)
  - Manages complex business processes
  - Handles task distribution
  - Ensures reliability and retries
- **Temporal Web UI**: Monitoring interface (Port: 8088)

### **Worker Services (Microservices)**
- **leads-worker-plaintext**: Document processing (PDF → Text)
- **leads-worker-llm**: AI/LLM processing (OpenAI/Azure AI)
- **leads-worker-cinode**: CRM integration (Cinode operations)
- **leads-worker-notifiers**: Notifications (Teams/Slack/Email)
- **leads-worker-workflows**: Main business logic workflows

### **External Services**
- **Cinode CRM**: External CRM system
- **Microsoft Teams/Slack**: Notification channels
- **OpenAI/Azure AI**: LLM services for document analysis
- **Email Service**: SMTP notifications

### **Storage & Infrastructure**
- **Azure Blob Storage**: File storage
- **Database**: Workflow state management
- **Azure Container Apps**: Runtime environment
- **Azure Container Registry**: Docker image storage
- **Application Insights**: Logging and monitoring

## 🔌 Frontend Integration Points

### **Primary API Endpoint**
Your frontend communicates **ONLY** with the `leads-adapter-api`:

```javascript
const API_BASE_URL = process.env.REACT_APP_API_URL || 'https://your-api-gateway.com';
```

### **Key API Operations**

#### 1. Start a Workflow
```javascript
// Upload document and start processing
const startWorkflow = async (formData) => {
  const response = await fetch(`${API_BASE_URL}/workflows/process-lead`, {
    method: 'POST',
    body: formData, // FormData with file
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json(); // Returns { workflowId, status }
};
```

#### 2. Check Workflow Status
```javascript
// Poll for workflow progress
const getWorkflowStatus = async (workflowId) => {
  const response = await fetch(`${API_BASE_URL}/workflows/${workflowId}/status`);
  return response.json(); // Returns { status, progress, results }
};
```

#### 3. Get Results
```javascript
// Retrieve processed results
const getWorkflowResults = async (workflowId) => {
  const response = await fetch(`${API_BASE_URL}/workflows/${workflowId}/results`);
  return response.json(); // Returns processed data
};
```

### **Real-time Updates**
```javascript
// WebSocket connection for live updates
const ws = new WebSocket(`${WS_BASE_URL}/workflows/${workflowId}/events`);
ws.onmessage = (event) => {
  const update = JSON.parse(event.data);
  // Update UI with progress
};
```

## 🎨 Expected Frontend Features

### **Core Functionality**
1. **File Upload Interface**
   - Drag & drop support
   - Multiple file formats (PDF, DOC, etc.)
   - File validation and preview

2. **Workflow Management**
   - Start new workflows
   - Monitor progress with progress bars
   - Display status updates in real-time

3. **Results Display**
   - Show processed document data
   - Display AI analysis results
   - Export/download capabilities

4. **Error Handling**
   - Network error recovery
   - User-friendly error messages
   - Retry mechanisms

### **UI Components Needed**
- File uploader with progress
- Workflow status dashboard
- Results viewer/table
- Notification system
- Loading states and spinners

## 🚀 Development Environment

### **Local Development**
```bash
# Your frontend app
npm start # Runs on http://localhost:3000

# API Gateway (separate repo)
# Runs on http://localhost:8080

# Temporal Web UI (for debugging)
# Available at http://localhost:8088
```

### **Environment Variables**
```env
REACT_APP_API_URL=http://localhost:8080
REACT_APP_WS_URL=ws://localhost:8080
REACT_APP_TEMPORAL_UI_URL=http://localhost:8088
```

## 🔧 Deployment

### **Azure Container Apps**
- **Dev**: `https://greensea-2681856e.swedencentral.azurecontainerapps.io`
- **Prod**: `https://orangemoss-33e6b54c.swedencentral.azurecontainerapps.io`

### **CI/CD Pipeline**
1. Push code to repository
2. Azure DevOps builds Docker image
3. Image pushed to Azure Container Registry
4. Azure Container Apps deploys automatically
5. Health checks verify deployment

## 🐛 Debugging & Monitoring

### **Tools Available**
- **Application Insights**: Centralized logging
- **Temporal Web UI**: Workflow visualization
- **Azure Portal**: Container logs and metrics

### **Common Debug Scenarios**
- Check API connectivity
- Monitor workflow progress in Temporal UI
- Review container logs for errors
- Verify file upload success

## 📋 Frontend Developer Checklist

### **Getting Started**
- [ ] Clone the leads-intake-app repository
- [ ] Set up local development environment
- [ ] Configure API endpoints
- [ ] Test file upload functionality
- [ ] Implement workflow status polling

### **Core Features**
- [ ] File upload with validation
- [ ] Progress tracking UI
- [ ] Results display components
- [ ] Error handling and user feedback
- [ ] Responsive design

### **Integration**
- [ ] API client setup
- [ ] WebSocket connections
- [ ] Authentication handling
- [ ] Error boundary implementation

## 🤝 Communication Patterns

### **Synchronous**
- Frontend ↔ API Gateway (HTTP REST)
- Direct API calls for immediate responses

### **Asynchronous**
- Workflow status updates via polling or WebSocket
- Background processing handled by workers
- Real-time notifications

## 💡 Key Benefits for Frontend Developers

1. **Simple Integration**: Only one API to integrate with
2. **Reliable Processing**: Temporal handles retries and failures
3. **Real-time Updates**: Live progress tracking
4. **Scalable Architecture**: Backend scales independently
5. **Clear Separation**: Focus on UI/UX, not business logic

## 🔗 Related Repositories

- `leads-adapter-api`: Your primary backend API
- `leads-worker-*`: Various worker services (backend)
- `cinode-helper-temporal-poc`: Demo/proof-of-concept

## 📞 Support & Resources

- **Temporal Web UI**: Monitor workflows and debug issues
- **Application Insights**: View logs and performance metrics
- **Azure DevOps**: CI/CD pipeline status and builds

---

**Remember**: As a frontend developer, you primarily interact with the `leads-adapter-api`. The complex microservices orchestration happens transparently behind the scenes via Temporal workflows.
